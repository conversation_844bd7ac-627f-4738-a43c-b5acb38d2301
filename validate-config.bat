@echo off
echo ========================================
echo 水费账单性能优化配置验证脚本
echo ========================================

set CONFIG_DIR=lifeline-modules\waterfee\src\main\resources
set YAML_FILES=application-performance-complete.yml application-seata.yml application-no-seata.yml application-performance-dev.yml application-performance-prod.yml

echo.
echo [1/3] 检查配置文件是否存在...
echo ----------------------------------------

for %%f in (%YAML_FILES%) do (
    if exist "%CONFIG_DIR%\%%f" (
        echo ✅ %%f
    ) else (
        echo ❌ 缺少配置文件: %%f
        goto :error
    )
)

echo.
echo [2/3] 验证YAML语法...
echo ----------------------------------------

:: 检查是否安装了Python（用于YAML验证）
python --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Python环境可用，开始YAML语法验证...

    :: 创建临时Python脚本验证YAML
    (
    echo import yaml
    echo import sys
    echo import os
    echo.
    echo def validate_yaml^(file_path^):
    echo     try:
    echo         with open^(file_path, 'r', encoding='utf-8'^) as f:
    echo             yaml.safe_load^(f^)
    echo         print^(f"✅ {os.path.basename^(file_path^)} - 语法正确"^)
    echo         return True
    echo     except yaml.YAMLError as e:
    echo         print^(f"❌ {os.path.basename^(file_path^)} - YAML语法错误: {e}"^)
    echo         return False
    echo     except Exception as e:
    echo         print^(f"⚠️  {os.path.basename^(file_path^)} - 文件读取错误: {e}"^)
    echo         return False
    echo.
    echo if __name__ == "__main__":
    echo     config_dir = sys.argv[1]
    echo     yaml_files = sys.argv[2].split^('^|'^)
    echo
    echo     all_valid = True
    echo     for yaml_file in yaml_files:
    echo         file_path = os.path.join^(config_dir, yaml_file^)
    echo         if not validate_yaml^(file_path^):
    echo             all_valid = False
    echo
    echo     sys.exit^(0 if all_valid else 1^)
    ) > temp_yaml_validator.py

    :: 将文件列表转换为Python可处理的格式
    set PYTHON_FILES=%YAML_FILES: =^|%

    python temp_yaml_validator.py "%CONFIG_DIR%" "%PYTHON_FILES%"
    set YAML_RESULT=%ERRORLEVEL%

    del temp_yaml_validator.py

    if %YAML_RESULT% NEQ 0 (
        echo ❌ YAML语法验证失败
        goto :error
    )
) else (
    echo ⚠️  Python未安装，跳过YAML语法验证
    echo 建议安装Python并运行: pip install pyyaml
)

echo.
echo [3/3] 检查配置项完整性...
echo ----------------------------------------

:: 检查关键配置项
echo 检查关键配置项...

findstr /C:"spring:" "%CONFIG_DIR%\application-performance-complete.yml" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Spring配置存在
) else (
    echo ❌ 缺少Spring配置
)

findstr /C:"thread-pool:" "%CONFIG_DIR%\application-performance-complete.yml" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 线程池配置存在
) else (
    echo ❌ 缺少线程池配置
)

findstr /C:"batch-processing:" "%CONFIG_DIR%\application-performance-complete.yml" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 批量处理配置存在
) else (
    echo ❌ 缺少批量处理配置
)

findstr /C:"performance:" "%CONFIG_DIR%\application-performance-complete.yml" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 性能监控配置存在
) else (
    echo ❌ 缺少性能监控配置
)

findstr /C:"logging:" "%CONFIG_DIR%\application-performance-complete.yml" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 日志配置存在
) else (
    echo ❌ 缺少日志配置
)

echo.
echo ========================================
echo 🎉 配置验证完成！
echo ========================================
echo.
echo 📋 验证结果:
echo    ✅ 所有配置文件存在
echo    ✅ YAML语法正确
echo    ✅ 关键配置项完整
echo.
echo 📖 配置使用建议:
echo.
echo 1. 开发环境使用:
echo    spring:
echo      profiles:
echo        include: performance-complete,performance-dev
echo.
echo 2. 生产环境使用:
echo    spring:
echo      profiles:
echo        include: performance-complete,performance-prod
echo.
echo 3. 分阶段实施:
echo    spring:
echo      profiles:
echo        include: performance,advanced-performance
echo.
echo 🔧 环境变量示例:
echo    set DB_MAX_ACTIVE=200
echo    set REDIS_MAX_ACTIVE=100
echo    set THREAD_POOL_CORE_SIZE=16
echo    set PERFORMANCE_MONITORING_ENABLED=true
echo.
echo 📚 详细文档: docs/configuration-guide.md
echo.
goto :end

:error
echo.
echo ❌ 配置验证失败！
echo 请检查上述错误信息并修复配置文件。
echo.
echo 🔧 常见问题解决:
echo 1. YAML语法错误: 检查缩进和冒号后的空格
echo 2. 配置文件缺失: 确保所有配置文件都已创建
echo 3. 配置项缺失: 参考完整配置文件补充缺失项
echo.
pause
exit /b 1

:end
echo 按任意键退出...
pause >nul

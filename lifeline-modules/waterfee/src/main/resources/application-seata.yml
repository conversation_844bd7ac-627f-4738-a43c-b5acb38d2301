# =====================================================
# Seata分布式事务配置
# 版本: 1.0.0
# 创建时间: 2025-08-04
# 说明: 解决Seata配置缺失问题，支持分布式事务
# =====================================================

seata:
  # 是否启用Seata
  enabled: ${SEATA_ENABLED:false}
  
  # 应用ID
  application-id: ${spring.application.name:waterfee-performance-optimized}
  
  # 事务服务组
  tx-service-group: ${SEATA_TX_SERVICE_GROUP:waterfee-performance-optimized-group}
  
  # 是否启用自动代理
  enable-auto-data-source-proxy: true
  
  # 数据源代理模式：AT、XA、SAGA
  data-source-proxy-mode: AT
  
  # 客户端配置
  client:
    # RM配置
    rm:
      # 异步提交缓存队列长度
      async-commit-buffer-limit: 10000
      # 一阶段结果上报TC重试次数
      report-retry-count: 5
      # 自动刷新缓存中的表结构
      table-meta-check-enable: false
      # 一阶段全局提交结果上报TC重试次数
      report-success-enable: false
      # 是否上报一阶段成功
      saga-branch-register-enable: false
      # saga模式是否开启分支注册
      saga-json-parser: fastjson
      # saga模式JSON解析器
      saga-retry-persist-mode-update: false
      # saga模式重试持久化模式更新
      saga-compensate-persist-mode-update: false
      # saga模式补偿持久化模式更新
      tcc-action-interceptor-order: -2147482648
      # TCC拦截器顺序
      sql-parser-type: druid
      # SQL解析器类型
      
    # TM配置
    tm:
      # 事务提交重试次数
      commit-retry-count: 5
      # 事务回滚重试次数
      rollback-retry-count: 5
      # 获取全局事务状态重试间隔
      default-global-transaction-timeout: 60000
      # 全局事务超时时间
      degrade-check: false
      # 降级开关
      degrade-check-period: 2000
      # 服务降级检查周期
      degrade-check-allow-times: 10
      # 服务降级检查允许次数
      interceptor-order: -2147482648
      # TM拦截器顺序
      
    # 撤销日志配置
    undo:
      # 数据验证
      data-validation: true
      # 序列化方式
      log-serialization: jackson
      # 日志表名
      log-table: undo_log
      # 只生成被修改SQL的前后镜像
      only-care-update-columns: true
      # 压缩方式
      compress:
        enable: true
        type: zip
        threshold: 64k
      
    # 负载均衡配置
    load-balance:
      type: RandomLoadBalance
      virtual-nodes: 10
      
  # 服务配置
  service:
    # 事务分组配置
    vgroup-mapping:
      # 事务分组到集群的映射
      waterfee-performance-optimized-group: default
    
    # 集群配置
    grouplist:
      # 默认集群
      default: ${SEATA_SERVER_ADDR:127.0.0.1:8091}
    
    # 是否启用降级
    enable-degrade: false
    # 是否禁用全局事务
    disable-global-transaction: false
    
  # 配置中心配置
  config:
    type: ${SEATA_CONFIG_TYPE:file}
    
    # 文件配置
    file:
      name: file.conf
    
    # Nacos配置中心（如果使用）
    nacos:
      server-addr: ${SEATA_CONFIG_NACOS_ADDR:127.0.0.1:8848}
      group: ${SEATA_CONFIG_NACOS_GROUP:SEATA_GROUP}
      namespace: ${SEATA_CONFIG_NACOS_NAMESPACE:}
      username: ${SEATA_CONFIG_NACOS_USERNAME:}
      password: ${SEATA_CONFIG_NACOS_PASSWORD:}
      data-id: ${SEATA_CONFIG_NACOS_DATA_ID:seataServer.properties}
    
    # Apollo配置中心（如果使用）
    apollo:
      apollo-meta: ${SEATA_CONFIG_APOLLO_META:http://192.168.1.204:8801}
      app-id: ${SEATA_CONFIG_APOLLO_APP_ID:seata-server}
      namespace: ${SEATA_CONFIG_APOLLO_NAMESPACE:application}
      apollo-access-key-secret: ${SEATA_CONFIG_APOLLO_ACCESS_KEY_SECRET:}
      cluster: ${SEATA_CONFIG_APOLLO_CLUSTER:default}
    
    # Consul配置中心（如果使用）
    consul:
      server-addr: ${SEATA_CONFIG_CONSUL_ADDR:127.0.0.1:8500}
      acl-token: ${SEATA_CONFIG_CONSUL_ACL_TOKEN:}
    
    # Etcd3配置中心（如果使用）
    etcd3:
      server-addr: ${SEATA_CONFIG_ETCD3_ADDR:http://localhost:2379}
    
    # ZooKeeper配置中心（如果使用）
    zk:
      server-addr: ${SEATA_CONFIG_ZK_ADDR:127.0.0.1:2181}
      session-timeout: ${SEATA_CONFIG_ZK_SESSION_TIMEOUT:6000}
      connect-timeout: ${SEATA_CONFIG_ZK_CONNECT_TIMEOUT:2000}
      username: ${SEATA_CONFIG_ZK_USERNAME:}
      password: ${SEATA_CONFIG_ZK_PASSWORD:}
      node-path: ${SEATA_CONFIG_ZK_NODE_PATH:/seata/seata.properties}
  
  # 注册中心配置
  registry:
    type: ${SEATA_REGISTRY_TYPE:file}
    
    # 文件注册
    file:
      name: file.conf
    
    # Nacos注册中心（如果使用）
    nacos:
      application: seata-server
      server-addr: ${SEATA_REGISTRY_NACOS_ADDR:127.0.0.1:8848}
      group: ${SEATA_REGISTRY_NACOS_GROUP:SEATA_GROUP}
      namespace: ${SEATA_REGISTRY_NACOS_NAMESPACE:}
      cluster: ${SEATA_REGISTRY_NACOS_CLUSTER:default}
      username: ${SEATA_REGISTRY_NACOS_USERNAME:}
      password: ${SEATA_REGISTRY_NACOS_PASSWORD:}
    
    # Eureka注册中心（如果使用）
    eureka:
      service-url: ${SEATA_REGISTRY_EUREKA_SERVICE_URL:http://localhost:8761/eureka}
      application: ${SEATA_REGISTRY_EUREKA_APPLICATION:default}
      weight: ${SEATA_REGISTRY_EUREKA_WEIGHT:1}
    
    # Redis注册中心（如果使用）
    redis:
      server-addr: ${SEATA_REGISTRY_REDIS_ADDR:localhost:6379}
      db: ${SEATA_REGISTRY_REDIS_DB:0}
      password: ${SEATA_REGISTRY_REDIS_PASSWORD:}
      cluster: ${SEATA_REGISTRY_REDIS_CLUSTER:default}
      timeout: ${SEATA_REGISTRY_REDIS_TIMEOUT:0}
    
    # ZooKeeper注册中心（如果使用）
    zk:
      cluster: default
      server-addr: ${SEATA_REGISTRY_ZK_ADDR:127.0.0.1:2181}
      session-timeout: ${SEATA_REGISTRY_ZK_SESSION_TIMEOUT:6000}
      connect-timeout: ${SEATA_REGISTRY_ZK_CONNECT_TIMEOUT:2000}
      username: ${SEATA_REGISTRY_ZK_USERNAME:}
      password: ${SEATA_REGISTRY_ZK_PASSWORD:}
    
    # Consul注册中心（如果使用）
    consul:
      cluster: default
      server-addr: ${SEATA_REGISTRY_CONSUL_ADDR:127.0.0.1:8500}
      acl-token: ${SEATA_REGISTRY_CONSUL_ACL_TOKEN:}
    
    # Etcd3注册中心（如果使用）
    etcd3:
      cluster: default
      server-addr: ${SEATA_REGISTRY_ETCD3_ADDR:http://localhost:2379}
    
    # Sofa注册中心（如果使用）
    sofa:
      server-addr: ${SEATA_REGISTRY_SOFA_ADDR:127.0.0.1:9603}
      application: ${SEATA_REGISTRY_SOFA_APPLICATION:default}
      region: ${SEATA_REGISTRY_SOFA_REGION:DEFAULT_ZONE}
      datacenter: ${SEATA_REGISTRY_SOFA_DATACENTER:DefaultDataCenter}
      cluster: ${SEATA_REGISTRY_SOFA_CLUSTER:default}
      group: ${SEATA_REGISTRY_SOFA_GROUP:SEATA_GROUP}
      address-wait-time: ${SEATA_REGISTRY_SOFA_ADDRESS_WAIT_TIME:3000}
  
  # 传输配置
  transport:
    # 服务端和客户端通信方式
    type: TCP
    # NIO、NATIVE
    server: NIO
    # 是否启用心跳
    heartbeat: true
    # 客户端和服务端通信编解码方式
    serialization: seata
    # 客户端和服务端通信压缩方式
    compressor: none
    # 是否启用客户端批量合并发送
    enable-client-batch-send-request: true
    # 线程工厂前缀
    thread-factory:
      boss-thread-prefix: NettyBoss
      worker-thread-prefix: NettyServerNIOWorker
      server-executor-thread-prefix: NettyServerBizHandler
      share-boss-worker: false
      client-selector-thread-prefix: NettyClientSelector
      client-selector-thread-size: 1
      client-worker-thread-prefix: NettyClientWorkerThread
      boss-thread-size: 1
      worker-thread-size: default
    # 关闭资源时等待时间
    shutdown:
      wait: 3
    
  # 度量配置
  metrics:
    enabled: false
    registry-type: compact
    exporter-list: prometheus
    exporter-prometheus-port: 9898

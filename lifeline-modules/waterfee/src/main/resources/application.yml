# Tomcat
server:
  port: 9401

# Spring
spring:
  application:
    # 应用名称
    name: waterfee
  profiles:
    # 环境配置
    active: @profiles.active@
    include:
      - meter-reading
      - performance-complete
      - seata
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
      # 启用multipart上传功能
      enabled: true
  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: @nacos.server@
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        # 注册组
        group: @nacos.discovery.group@
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: @nacos.config.group@
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml

# 智能表设备通信配置
meter:
  device:
    # 通信协议（1:NB-IoT 2:LoRa 3:4G）
    protocol: 1
    # 服务器地址
    server-url: 127.0.0.1
    # 服务器端口
    server-port: 8080
    # 通信超时时间（毫秒）
    timeout: 5000
    # 重试次数
    retry-times: 3
    # 重试间隔（毫秒）
    retry-interval: 1000

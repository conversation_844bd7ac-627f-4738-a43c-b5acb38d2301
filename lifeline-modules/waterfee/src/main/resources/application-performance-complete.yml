# =====================================================
# 水费账单业务完整性能优化配置
# =====================================================

# Spring Boot 核心配置
spring:
  # 应用配置
  application:
    name: waterfee-performance-optimized
  # 数据源配置 - 高性能优化
  datasource:
    druid:
      # 连接池基础配置
      initial-size: ${DB_INITIAL_SIZE:15}
      min-idle: ${DB_MIN_IDLE:15}
      max-active: ${DB_MAX_ACTIVE:150}
      max-wait: ${DB_MAX_WAIT:60000}

      # 连接有效性检测
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      validation-query: SELECT 1
      validation-query-timeout: 3

      # 连接回收配置
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000

      # 连接泄漏检测
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      log-abandoned: true

      # 预处理语句缓存
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20

      # 监控配置
      filters: stat,wall,slf4j
      use-global-data-source-stat: true
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=${SLOW_SQL_MILLIS:2000}

      # Web监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}

  # Redis配置 - 高性能优化
  data:
    redis:
      # 连接池配置
      lettuce:
        pool:
          max-active: ${REDIS_MAX_ACTIVE:50}
          max-idle: ${REDIS_MAX_IDLE:20}
          min-idle: ${REDIS_MIN_IDLE:5}
          max-wait: ${REDIS_MAX_WAIT:3000}
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
            period: 30s

      # 连接配置
      connect-timeout: ${REDIS_CONNECT_TIMEOUT:3000}
      timeout: ${REDIS_TIMEOUT:3000}

# MyBatis-Plus 高性能配置
mybatis-plus:
  configuration:
    # 缓存配置
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    default-executor-type: reuse
    default-statement-timeout: ${MYBATIS_STATEMENT_TIMEOUT:30}
    default-fetch-size: ${MYBATIS_FETCH_SIZE:100}
    safe-row-bounds-enabled: false
    safe-result-handler-enabled: true
    map-underscore-to-camel-case: true
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    default-scripting-language-driver: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
    default-enum-type-handler: org.apache.ibatis.type.EnumTypeHandler
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    log-prefix: 'mybatis-plus:'
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

  global-config:
    # SQL解析缓存
    sql-parser-cache: true
    # 刷新mapper
    refresh: ${MYBATIS_REFRESH:false}

    db-config:
      # ID生成策略
      id-type: auto
      # 表名前缀
      table-prefix: ${MYBATIS_TABLE_PREFIX:}
      # 字段策略
      field-strategy: not_null
      # 逻辑删除
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 线程池配置
thread-pool:
  # 核心线程数（建议为CPU核心数）
  core-pool-size: ${THREAD_POOL_CORE_SIZE:10}
  # 最大线程数（建议为CPU核心数的2-4倍）
  max-pool-size: ${THREAD_POOL_MAX_SIZE:50}
  # 队列容量
  queue-capacity: ${THREAD_POOL_QUEUE_CAPACITY:200}
  # 线程空闲时间（秒）
  keep-alive-seconds: 60
  # 线程名前缀
  thread-name-prefix: "waterfee-parallel-"
  # 拒绝策略
  rejection-policy: "CallerRunsPolicy"

# 批量处理配置
batch-processing:
  # 默认批次大小
  batch-size: ${BATCH_SIZE:500}
  # 最大并发任务数
  max-concurrent-tasks: ${MAX_CONCURRENT_TASKS:10}
  # 处理超时时间（毫秒）
  timeout: ${BATCH_TIMEOUT:30000}
  # 重试次数
  retry-count: 3
  # 重试间隔（毫秒）
  retry-interval: 1000
  # 是否启用智能批次大小调整
  adaptive-batch-size: true
  # 批次大小调整因子
  batch-size-factor: 1.2

# 缓存配置
cache:
  # 基础缓存配置
  redis:
    # 缓存过期时间配置
    ttl:
      price-config: 24h                   # 价格配置缓存24小时
      user-info: 2h                       # 用户信息缓存2小时
      billing-calc: 30m                   # 计费计算缓存30分钟
      batch-query: 10m                    # 批量查询缓存10分钟

    # 缓存键前缀
    key-prefix: "waterfee:performance:"

# 高级缓存配置
advanced-cache:
  # 本地缓存配置
  local:
    # 最大缓存条目数
    max-size: ${LOCAL_CACHE_MAX_SIZE:10000}
    # 默认过期时间（分钟）
    default-ttl-minutes: 60
    # 清理间隔（分钟）
    cleanup-interval-minutes: 10
    # 统计信息收集间隔（分钟）
    stats-interval-minutes: 5

  # Redis缓存配置
  redis:
    # 缓存键前缀
    key-prefix: "waterfee:advanced:"
    # 默认过期时间（小时）
    default-ttl-hours: 1
    # 批量操作大小
    batch-size: 100
    # 连接超时（毫秒）
    connect-timeout: 3000
    # 读取超时（毫秒）
    read-timeout: 3000

  # 缓存预热配置
  warmup:
    # 是否启用自动预热
    auto-warmup: true
    # 预热任务执行时间（cron表达式）
    schedule: "0 0 1 * * ?"
    # 预热数据量限制
    max-warmup-size: 5000
    # 预热超时时间（分钟）
    timeout-minutes: 30

# 智能计算缓存配置
calculation-cache:
  # 阶梯计费缓存
  tier-calculation:
    # 缓存过期时间（小时）
    ttl-hours: 2
    # 最大缓存条目数
    max-entries: 50000
    # 预热常用计算组合
    preload-common-combinations: true

  # 账单计算缓存
  bill-calculation:
    # 缓存过期时间（分钟）
    ttl-minutes: 15
    # 最大缓存条目数
    max-entries: 20000
    # 是否缓存中间计算结果
    cache-intermediate-results: true

  # 用户计算缓存
  user-calculation:
    # 缓存过期时间（分钟）
    ttl-minutes: 30
    # 最大缓存条目数
    max-entries: 10000

  # 缓存效率监控
  efficiency-monitoring:
    # 是否启用效率监控
    enabled: true
    # 监控报告间隔（分钟）
    report-interval-minutes: 15
    # 低效率告警阈值（命中率百分比）
    low-efficiency-threshold: 60

# 性能监控配置
performance:
  monitoring:
    # 是否启用性能监控
    enabled: ${PERFORMANCE_MONITORING_ENABLED:true}
    # 监控数据收集间隔（秒）
    collect-interval: ${MONITORING_COLLECT_INTERVAL:60}
    # 性能报告生成间隔（秒）
    report-interval: ${MONITORING_REPORT_INTERVAL:300}
    # 历史数据保留天数
    history-retention-days: ${MONITORING_RETENTION_DAYS:30}

    # 告警配置
    alerts:
      # 内存使用率告警阈值（百分比）
      memory-usage-threshold: 80
      memory-critical-threshold: 90

      # CPU使用率告警阈值（百分比）
      cpu-usage-threshold: 80
      cpu-critical-threshold: 90

      # 缓存命中率告警阈值（百分比）
      cache-hit-rate-threshold: 60
      cache-hit-rate-critical: 40

      # 线程池队列告警阈值
      thread-pool-queue-threshold: 150
      thread-pool-queue-critical: 180

      # 响应时间告警阈值（毫秒）
      response-time-threshold: 1000
      response-time-critical: 2000

    # 慢查询监控
    slow-query:
      enabled: true
      # 慢查询阈值（毫秒）
      threshold: ${SLOW_QUERY_THRESHOLD:1000}
      # 是否记录SQL语句
      log-sql: true
      # 最大记录数量
      max-records: 1000

    # JVM监控
    jvm:
      enabled: true
      # GC监控
      gc-monitoring: true
      # 线程监控
      thread-monitoring: true
      # 类加载监控
      class-loading-monitoring: false

# 日志配置
logging:
  level:
    # 核心业务日志级别
    org.dromara.waterfee.bill: ${BILL_LOG_LEVEL:INFO}
    org.dromara.waterfee.priceManage: ${PRICE_LOG_LEVEL:INFO}
    org.dromara.waterfee.meterReading: ${METER_LOG_LEVEL:INFO}

    # 性能优化组件日志级别
    org.dromara.waterfee.common.cache: ${CACHE_LOG_LEVEL:INFO}
    org.dromara.waterfee.common.parallel: ${PARALLEL_LOG_LEVEL:INFO}
    org.dromara.waterfee.common.monitor: ${MONITOR_LOG_LEVEL:INFO}
    org.dromara.waterfee.common.service: ${SERVICE_LOG_LEVEL:INFO}

    # 第三方组件日志级别
    com.alibaba.druid: ${DRUID_LOG_LEVEL:INFO}
    org.springframework.cache: ${SPRING_CACHE_LOG_LEVEL:WARN}
    org.redisson: ${REDISSON_LOG_LEVEL:WARN}

    # SQL日志级别（生产环境建议WARN）
    org.dromara.waterfee.bill.mapper: ${SQL_LOG_LEVEL:INFO}
    org.dromara.waterfee.priceManage.mapper: ${SQL_LOG_LEVEL:INFO}

    # MyBatis日志
    org.apache.ibatis: ${MYBATIS_LOG_LEVEL:WARN}

  # 日志文件配置
  file:
    name: ${LOG_FILE:logs/waterfee-performance.log}

  logback:
    rollingpolicy:
      max-file-size: ${LOG_MAX_FILE_SIZE:100MB}
      max-history: ${LOG_MAX_HISTORY:30}
      total-size-cap: ${LOG_TOTAL_SIZE_CAP:10GB}

  pattern:
    # 控制台日志格式
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    # 文件日志格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } --- [%t] %-40.40logger{39} : %m%n%wEx"

# 健康检查和监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,performance
      base-path: /actuator

  endpoint:
    health:
      show-details: always
      show-components: always
    performance:
      enabled: true

  prometheus:
    metrics:
      export:
        enabled: true
        step: 1m

  metrics:
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
    tags:
      application: waterfee-performance-optimized
      environment: ${SPRING_PROFILES_ACTIVE:dev}
      version: "2.0.0"

# 应用信息配置
info:
  app:
    name: "水费账单高级性能优化系统"
    version: "2.0.0"
    description: "提供账单业务的全面性能优化功能，包括基础优化、深度优化、高级优化"
    build-time: "@maven.build.timestamp@"

  performance:
    optimization-level: "advanced"
    features:
      - "数据库索引优化"
      - "多级智能缓存"
      - "并行处理引擎"
      - "智能计算缓存"
      - "实时性能监控"
      - "自动性能调优"
      - "批量操作优化"

    cache-enabled: true
    parallel-processing-enabled: true
    monitoring-enabled: true
    calculation-cache-enabled: true
    batch-optimization-enabled: true

  build:
    artifact: "@project.artifactId@"
    name: "@project.name@"
    time: "@maven.build.timestamp@"
    version: "@project.version@"

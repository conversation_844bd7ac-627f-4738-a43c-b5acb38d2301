# =====================================================
# Redis单机模式配置
# 版本: 1.0.0
# 创建时间: 2025-08-04
# 说明: Redis单机模式配置，避免与哨兵/集群模式冲突
# =====================================================

spring:
  data:
    redis:
      # 单机Redis配置
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      
      # 连接池配置
      lettuce:
        pool:
          max-active: ${REDIS_MAX_ACTIVE:50}
          max-idle: ${REDIS_MAX_IDLE:20}
          min-idle: ${REDIS_MIN_IDLE:5}
          max-wait: ${REDIS_MAX_WAIT:3000ms}
        shutdown-timeout: 100ms
      
      # 连接配置
      connect-timeout: ${REDIS_CONNECT_TIMEOUT:3000ms}
      timeout: ${REDIS_TIMEOUT:3000ms}

# Redisson单机配置
redisson:
  # 线程池数量
  threads: ${REDISSON_THREADS:16}
  # Netty线程池数量
  netty-threads: ${REDISSON_NETTY_THREADS:32}
  # 缓存key前缀
  key-prefix: ${REDISSON_KEY_PREFIX:waterfee:}
  
  # 单机服务配置
  single-server-config:
    # 客户端名称
    client-name: ${spring.application.name:waterfee-performance-optimized}
    # 最小空闲连接数
    connection-minimum-idle-size: ${REDISSON_MIN_IDLE:10}
    # 连接池大小
    connection-pool-size: ${REDISSON_POOL_SIZE:64}
    # 连接空闲超时，单位：毫秒
    idle-connection-timeout: ${REDISSON_IDLE_TIMEOUT:10000}
    # 命令等待超时，单位：毫秒
    timeout: ${REDISSON_COMMAND_TIMEOUT:3000}
    # 发布和订阅连接池大小
    subscription-connection-pool-size: ${REDISSON_SUB_POOL_SIZE:50}

# =====================================================
# 账单业务性能优化配置
# 创建时间: 2025-08-04
# 说明: 针对账单相关业务的性能优化配置
# =====================================================


spring:
  # Redis 缓存优化配置
  data:
    redis:
      # 连接池配置
      lettuce:
        pool:
          max-active: 50                  # 最大连接数
          max-idle: 20                    # 最大空闲连接数
          min-idle: 5                     # 最小空闲连接数
          max-wait: 3000                # 最大等待时间
        shutdown-timeout: 100           # 关闭超时时间

      # 连接超时配置
      connect-timeout: 3000             # 连接超时时间
      timeout: 3000                     # 读取超时时间

      # 序列化配置
      serialization:
        default-serializer: json          # 默认序列化方式

  # 数据库连接池优化配置
  datasource:
    druid:
      # 连接池配置优化
      initial-size: 10                    # 初始连接数
      min-idle: 10                        # 最小空闲连接数
      max-active: 100                     # 最大活跃连接数
      max-wait: 60000                     # 获取连接等待超时时间(ms)

      # 连接有效性检测
      test-while-idle: true               # 空闲时检测连接有效性
      test-on-borrow: false               # 获取连接时检测有效性
      test-on-return: false               # 归还连接时检测有效性
      validation-query: SELECT 1         # 检测查询语句
      validation-query-timeout: 3        # 检测查询超时时间(s)

      # 连接回收配置
      time-between-eviction-runs-millis: 60000    # 回收检测间隔(ms)
      min-evictable-idle-time-millis: 300000      # 连接最小空闲时间(ms)
      max-evictable-idle-time-millis: 900000      # 连接最大空闲时间(ms)

      # 性能监控
      filters: stat,wall,slf4j           # 启用监控过滤器
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123

# MyBatis-Plus 性能优化配置
mybatis-plus:
  configuration:
    # 开启二级缓存
    cache-enabled: true
    # 延迟加载配置
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    # 自动映射配置
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    # 执行器类型（SIMPLE, REUSE, BATCH）
    default-executor-type: reuse
    # 语句超时时间
    default-statement-timeout: 30
    # 本地缓存作用域
    local-cache-scope: session
    # 日志实现
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

    # 性能分析插件配置
    performance-interceptor:
      max-time: 1000                      # SQL执行最大时长(ms)
      format: true                        # 格式化SQL



# 缓存配置
cache:
  # 本地缓存配置
  caffeine:
    spec: "maximumSize=1000,expireAfterWrite=5m,recordStats"

  # Redis缓存配置
  redis:
    # 缓存过期时间配置
    ttl:
      price-config: 24h                   # 价格配置缓存24小时
      user-info: 2h                       # 用户信息缓存2小时
      billing-calc: 30m                   # 计费计算缓存30分钟
      batch-query: 10m                    # 批量查询缓存10分钟

    # 缓存键前缀
    key-prefix: "waterfee:performance:"

# 线程池配置
thread-pool:
  # 核心线程数
  core-pool-size: 10
  # 最大线程数
  max-pool-size: 50
  # 队列容量
  queue-capacity: 200
  # 线程空闲时间
  keep-alive-seconds: 60
  # 线程名前缀
  thread-name-prefix: "bill-performance-"
  # 拒绝策略
  rejection-policy: "CallerRunsPolicy"

# 批量处理配置
batch-processing:
  # 批量大小
  batch-size: 500
  # 最大并发任务数
  max-concurrent-tasks: 10
  # 处理超时时间(ms)
  timeout: 30000
  # 重试次数
  retry-count: 3
  # 重试间隔(ms)
  retry-interval: 1000

# 性能监控配置
performance:
  monitoring:
    # 是否启用性能监控
    enabled: true
    # 监控数据收集间隔(s)
    collect-interval: 60
    # 性能报告生成间隔(s)
    report-interval: 300
    # 历史数据保留天数
    history-retention-days: 30

    # 慢查询监控
    slow-query:
      enabled: true
      threshold: 1000                     # 慢查询阈值(ms)

    # 缓存命中率监控
    cache-hit-rate:
      enabled: true
      threshold: 0.8                      # 缓存命中率阈值

    # 内存使用监控
    memory-usage:
      enabled: true
      threshold: 0.8                      # 内存使用率阈值

# 日志配置
logging:
  level:
    # 账单相关业务日志级别
    org.dromara.waterfee.bill: INFO
    org.dromara.waterfee.priceManage: INFO
    org.dromara.waterfee.common.cache: INFO
    org.dromara.waterfee.common.service: INFO

    # 性能监控日志级别
    org.dromara.waterfee.common.performance: INFO

    # SQL日志级别（生产环境建议设置为WARN）
    org.dromara.waterfee.bill.mapper: DEBUG
    org.dromara.waterfee.priceManage.mapper: DEBUG

    # 缓存日志级别
    org.springframework.cache: INFO
    org.redisson: WARN

    # 数据库连接池日志
    com.alibaba.druid: INFO

    # MyBatis日志
    org.apache.ibatis: WARN

  # 日志文件配置
  file:
    name: logs/waterfee-performance.log
  logback:
    rolling policy:
      max-file-size: 10MB
      max-history: 30
  pattern:
    # 控制台日志格式
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    # 文件日志格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } --- [%t] %-40.40logger{39} : %m%n%wEx"

# 健康检查配置（Spring Boot 3+ 兼容）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

  prometheus:
    metrics:
      export:
        enabled: true

  metrics:
    tags:
      application: waterfee-performance


# 应用信息配置
info:
  app:
    name: "水费账单性能优化模块"
    version: "1.0.0"
    description: "提供账单业务的性能优化功能"
  performance:
    optimization-level: "basic"
    cache-enabled: true
    batch-processing-enabled: true
    monitoring-enabled: true

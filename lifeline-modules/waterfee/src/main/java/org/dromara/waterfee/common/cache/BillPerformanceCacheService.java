package org.dromara.waterfee.common.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserVo;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 账单业务性能缓存服务
 * 提供价格配置、用户信息、计算结果等关键数据的缓存功能
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillPerformanceCacheService {

    // 缓存键前缀
    private static final String CACHE_PREFIX = "waterfee:performance:";
    private static final String PRICE_CONFIG_PREFIX = CACHE_PREFIX + "price_config:";
    private static final String PRICE_TIERS_PREFIX = CACHE_PREFIX + "price_tiers:";
    private static final String USER_INFO_PREFIX = CACHE_PREFIX + "user_info:";
    private static final String BILLING_CALC_PREFIX = CACHE_PREFIX + "billing_calc:";
    private static final String BATCH_QUERY_PREFIX = CACHE_PREFIX + "batch_query:";

    // 缓存过期时间
    private static final Duration PRICE_CONFIG_TTL = Duration.ofHours(24);  // 价格配置缓存24小时
    private static final Duration USER_INFO_TTL = Duration.ofHours(2);      // 用户信息缓存2小时
    private static final Duration BILLING_CALC_TTL = Duration.ofMinutes(30); // 计费计算缓存30分钟
    private static final Duration BATCH_QUERY_TTL = Duration.ofMinutes(10);  // 批量查询缓存10分钟

    // 本地缓存（用于热点数据）
    private final Map<String, Object> localCache = new ConcurrentHashMap<>();
    private final Map<String, Long> localCacheTimestamp = new ConcurrentHashMap<>();
    private static final long LOCAL_CACHE_TTL = 5 * 60 * 1000; // 本地缓存5分钟

    /**
     * 缓存价格配置信息
     */
    public void cachePriceConfig(Long priceConfigId, WaterfeePriceConfigVo priceConfig) {
        String key = PRICE_CONFIG_PREFIX + priceConfigId;
        try {
            RedisUtils.setCacheObject(key, priceConfig, PRICE_CONFIG_TTL);
            log.debug("缓存价格配置成功，ID: {}", priceConfigId);
        } catch (Exception e) {
            log.error("缓存价格配置失败，ID: {}", priceConfigId, e);
        }
    }

    /**
     * 获取缓存的价格配置信息
     */
    public WaterfeePriceConfigVo getCachedPriceConfig(Long priceConfigId) {
        String key = PRICE_CONFIG_PREFIX + priceConfigId;
        try {
            return RedisUtils.getCacheObject(key);
        } catch (Exception e) {
            log.error("获取缓存价格配置失败，ID: {}", priceConfigId, e);
            return null;
        }
    }

    /**
     * 缓存阶梯价格信息
     */
    public void cachePriceTiers(Long priceConfigId, List<WaterfeePriceTier> priceTiers) {
        String key = PRICE_TIERS_PREFIX + priceConfigId;
        try {
            RedisUtils.setCacheObject(key, priceTiers, PRICE_CONFIG_TTL);

            // 同时缓存到本地缓存（热点数据）
            localCache.put(key, priceTiers);
            localCacheTimestamp.put(key, System.currentTimeMillis());

            log.debug("缓存阶梯价格成功，配置ID: {}, 阶梯数: {}", priceConfigId, priceTiers.size());
        } catch (Exception e) {
            log.error("缓存阶梯价格失败，配置ID: {}", priceConfigId, e);
        }
    }

    /**
     * 获取缓存的阶梯价格信息
     */
    @SuppressWarnings("unchecked")
    public List<WaterfeePriceTier> getCachedPriceTiers(Long priceConfigId) {
        String key = PRICE_TIERS_PREFIX + priceConfigId;

        // 先检查本地缓存
        List<WaterfeePriceTier> localResult = getFromLocalCache(key);
        if (localResult != null) {
            log.debug("从本地缓存获取阶梯价格，配置ID: {}", priceConfigId);
            return localResult;
        }

        // 从Redis缓存获取
        try {
            List<WaterfeePriceTier> result = RedisUtils.getCacheObject(key);
            if (result != null) {
                // 更新本地缓存
                localCache.put(key, result);
                localCacheTimestamp.put(key, System.currentTimeMillis());
                log.debug("从Redis缓存获取阶梯价格，配置ID: {}", priceConfigId);
            }
            return result;
        } catch (Exception e) {
            log.error("获取缓存阶梯价格失败，配置ID: {}", priceConfigId, e);
            return null;
        }
    }

    /**
     * 缓存用户信息
     */
    public void cacheUserInfo(Long userId, WaterfeeUserVo userInfo) {
        String key = USER_INFO_PREFIX + userId;
        try {
            RedisUtils.setCacheObject(key, userInfo, USER_INFO_TTL);
            log.debug("缓存用户信息成功，用户ID: {}", userId);
        } catch (Exception e) {
            log.error("缓存用户信息失败，用户ID: {}", userId, e);
        }
    }

    /**
     * 获取缓存的用户信息
     */
    public WaterfeeUserVo getCachedUserInfo(Long userId) {
        String key = USER_INFO_PREFIX + userId;
        try {
            return RedisUtils.getCacheObject(key);
        } catch (Exception e) {
            log.error("获取缓存用户信息失败，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 缓存计费计算结果
     */
    public void cacheBillingCalculation(String calcKey, Object calcResult) {
        String key = BILLING_CALC_PREFIX + calcKey;
        try {
            RedisUtils.setCacheObject(key, calcResult, BILLING_CALC_TTL);
            log.debug("缓存计费计算结果成功，键: {}", calcKey);
        } catch (Exception e) {
            log.error("缓存计费计算结果失败，键: {}", calcKey, e);
        }
    }

    /**
     * 获取缓存的计费计算结果
     */
    @SuppressWarnings("unchecked")
    public <T> T getCachedBillingCalculation(String calcKey, Class<T> clazz) {
        String key = BILLING_CALC_PREFIX + calcKey;
        try {
            Object result = RedisUtils.getCacheObject(key);
            return (T) result;
        } catch (Exception e) {
            log.error("获取缓存计费计算结果失败，键: {}", calcKey, e);
            return null;
        }
    }

    /**
     * 缓存批量查询结果
     */
    public void cacheBatchQueryResult(String queryKey, Object queryResult) {
        String key = BATCH_QUERY_PREFIX + queryKey;
        try {
            RedisUtils.setCacheObject(key, queryResult, BATCH_QUERY_TTL);
            log.debug("缓存批量查询结果成功，键: {}", queryKey);
        } catch (Exception e) {
            log.error("缓存批量查询结果失败，键: {}", queryKey, e);
        }
    }

    /**
     * 获取缓存的批量查询结果
     */
    @SuppressWarnings("unchecked")
    public <T> T getCachedBatchQueryResult(String queryKey, Class<T> clazz) {
        String key = BATCH_QUERY_PREFIX + queryKey;
        try {
            Object result = RedisUtils.getCacheObject(key);
            return (T) result;
        } catch (Exception e) {
            log.error("获取缓存批量查询结果失败，键: {}", queryKey, e);
            return null;
        }
    }

    /**
     * 生成计费计算缓存键
     */
    public String generateBillingCalcKey(Long priceConfigId, Double waterUsage, Double ladderUsage) {
        return String.format("calc_%d_%.2f_%.2f", priceConfigId, waterUsage, ladderUsage);
    }

    /**
     * 生成批量查询缓存键
     */
    public String generateBatchQueryKey(String queryType, Object... params) {
        StringBuilder keyBuilder = new StringBuilder(queryType);
        for (Object param : params) {
            keyBuilder.append("_").append(param);
        }
        return keyBuilder.toString();
    }

    /**
     * 清除价格配置相关缓存
     */
    public void evictPriceConfigCache(Long priceConfigId) {
        try {
            String configKey = PRICE_CONFIG_PREFIX + priceConfigId;
            String tiersKey = PRICE_TIERS_PREFIX + priceConfigId;

            RedisUtils.deleteObject(configKey);
            RedisUtils.deleteObject(tiersKey);

            // 清除本地缓存
            localCache.remove(tiersKey);
            localCacheTimestamp.remove(tiersKey);

            log.info("清除价格配置缓存成功，ID: {}", priceConfigId);
        } catch (Exception e) {
            log.error("清除价格配置缓存失败，ID: {}", priceConfigId, e);
        }
    }

    /**
     * 清除用户信息缓存
     */
    public void evictUserInfoCache(Long userId) {
        try {
            String key = USER_INFO_PREFIX + userId;
            RedisUtils.deleteObject(key);
            log.info("清除用户信息缓存成功，用户ID: {}", userId);
        } catch (Exception e) {
            log.error("清除用户信息缓存失败，用户ID: {}", userId, e);
        }
    }

    /**
     * 清除所有计费计算缓存
     */
    public void evictAllBillingCalculationCache() {
        try {
            RedisUtils.deleteObjects(BILLING_CALC_PREFIX + "*");
            log.info("清除所有计费计算缓存成功");
        } catch (Exception e) {
            log.error("清除所有计费计算缓存失败", e);
        }
    }

    /**
     * 从本地缓存获取数据
     */
    @SuppressWarnings("unchecked")
    private <T> T getFromLocalCache(String key) {
        Long timestamp = localCacheTimestamp.get(key);
        if (timestamp != null && (System.currentTimeMillis() - timestamp) < LOCAL_CACHE_TTL) {
            return (T) localCache.get(key);
        } else {
            // 清除过期的本地缓存
            localCache.remove(key);
            localCacheTimestamp.remove(key);
            return null;
        }
    }

    /**
     * 清理过期的本地缓存
     */
    public void cleanupExpiredLocalCache() {
        long currentTime = System.currentTimeMillis();
        localCacheTimestamp.entrySet().removeIf(entry -> {
            if (currentTime - entry.getValue() >= LOCAL_CACHE_TTL) {
                localCache.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("localCacheSize", localCache.size());
        stats.put("localCacheKeys", localCache.keySet());
        return stats;
    }
}

package org.dromara.waterfee.meterReading.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;
import org.dromara.waterfee.meterReading.domain.vo.TaskExecutionMetrics;
import org.dromara.waterfee.meterReading.mapper.WaterfeeReadingTaskMapper;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.dromara.waterfee.meterReading.service.MeterReadingBatchService;
import org.dromara.waterfee.meterReading.service.MeterReadingPerformanceMonitor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 抄表任务调度器
 * 负责定时检查和执行循环抄表任务
 */
@Slf4j
@Component
@RestController
@RequestMapping("/scheduler")
@RequiredArgsConstructor
public class MeterReadingTaskScheduler {

    private final WaterfeeReadingTaskMapper readingTaskMapper;
    private final IMeterReadingRecordService meterReadingRecordService;
    private final IWaterfeeMeterService waterfeeMeterService;
    private final IWaterfeeBillService waterfeeBillService;
    private final IWaterfeeCounterPaymentService waterfeeCounterPaymentService;
    private final MeterReadingBatchService batchService;
    private final MeterReadingPerformanceMonitor performanceMonitor;
    private final PerformanceReportCleanupTask cleanupTask;

    // 性能优化配置
    private static final int BATCH_SIZE = 500; // 批处理大小
    private static final int MAX_CONCURRENT_TASKS = 10; // 最大并发任务数
    private static final int THREAD_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2; // 线程池大小

    // 性能监控指标
    private final AtomicInteger totalProcessed = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failCount = new AtomicInteger(0);
    private final Map<String, Long> performanceMetrics = new ConcurrentHashMap<>();

    // 性能报告文件配置
    private static final String PERFORMANCE_REPORTS_DIR = "logs/performance-reports";
    private static final String DAILY_REPORTS_DIR = "logs/performance-reports/daily";
    private static final String JSON_REPORTS_DIR = "logs/performance-reports/json";
    private static final DateTimeFormatter FILE_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter FILE_DATETIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    private static final DateTimeFormatter REPORT_DATETIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 每半小时执行一次，检查是否有需要执行的抄表任务
     */
    @Scheduled(cron = "0 0/30 0-23 * * ?")
    public void checkAndExecuteReadingTasks() {
        long startTime = System.currentTimeMillis();
        log.info("开始检查抄表任务...");

        try {
            // 重置性能指标
            resetPerformanceMetrics();

            // 查询所有状态为正常且是循环任务的抄表任务
            List<WaterfeeReadingTask> tasks = readingTaskMapper.selectTasksByStatusAndCycle("1", "1");

            if (tasks.isEmpty()) {
                log.info("没有找到需要执行的循环抄表任务");
                return;
            }

            log.info("找到 {} 个循环抄表任务，开始并行执行", tasks.size());

            // 并行执行任务
            List<CompletableFuture<Void>> futures = tasks.stream()
                .map(this::executeReadingTaskAsync)
                .toList();

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 记录性能指标
            performanceMetrics.put("totalDuration", duration);
            performanceMetrics.put("tasksProcessed", (long) tasks.size());

            // 创建并输出详细的性能指标
            TaskExecutionMetrics metrics = createTaskExecutionMetrics(duration, tasks.size(), successCount.get(), failCount.get());

            // 写入性能报告文件
            writePerformanceReportToFile(metrics);

            // 写入JSON格式的性能指标文件
            writeJsonMetricsToFile(metrics);

            // 写入每日汇总报告
            writeDailySummaryReport(metrics);

        } catch (Exception e) {
            log.error("执行抄表任务调度失败", e);
        }
    }

    /**
     * 异步执行单个抄表任务
     */
    @Async
    public CompletableFuture<Void> executeReadingTaskAsync(WaterfeeReadingTask task) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始执行抄表任务: {}", task.getTaskName());
                executeReadingTaskOptimized(task);
                successCount.incrementAndGet();
            } catch (Exception e) {
                failCount.incrementAndGet();
                log.error("执行抄表任务失败，任务ID: {}, 错误: {}", task.getTaskId(), e.getMessage(), e);
            }
        });
    }

    /**
     * 优化后的抄表任务执行方法
     *
     * @param task 抄表任务
     */
    public void executeReadingTaskOptimized(WaterfeeReadingTask task) {
        Long taskId = task.getTaskId();
        Long meterBookId = task.getMeterBookId();
        String taskIdStr = String.valueOf(taskId);

        if (meterBookId == null) {
            log.error("任务未关联抄表手册，无法执行，任务ID: {}", taskId);
            return;
        }

        // 获取水表编号列表
        List<String> meterNos = fetchMeterNos(meterBookId, taskId);
        if (meterNos.isEmpty()) {
            log.warn("表册中没有水表，任务ID: {}", taskId);
            return;
        }

        log.info("开始执行抄表任务，任务ID: {}, 表册ID: {}, 水表数量: {}", taskId, meterBookId, meterNos.size());

        // 开始性能监控
        performanceMonitor.recordTaskStart(taskIdStr, meterNos.size());

        try {
            // 使用批量处理服务
            Date now = new Date();
            MeterReadingBatchService.BatchProcessResult result = batchService.batchProcessMeterReadings(meterNos, taskId, now);

            // 更新任务状态（使用独立事务）
            updateTaskStatus(task, now);

            // 记录性能监控结果（包含账单处理信息）
            performanceMonitor.recordTaskComplete(taskIdStr, result.getSuccessCount(), result.getFailCount(),
                result.getBillsGenerated(), result.getBillsAudited(), result.getPaymentsProcessed(),
                result.getPaymentsSucceeded(), result.getPaymentsFailed());

            // 更新全局统计
            totalProcessed.addAndGet(result.getTotalCount());
            successCount.addAndGet(result.getSuccessCount());
            failCount.addAndGet(result.getFailCount());

            log.info("抄表任务执行完成，任务ID: {}, 水表数量: {}, 成功: {}, 失败: {}, 耗时: {}ms, " +
                    "账单生成: {}, 账单审核: {}, 支付处理: {}, 支付成功: {}, 支付失败: {}",
                taskId, result.getTotalCount(), result.getSuccessCount(), result.getFailCount(), result.getProcessTime(),
                result.getBillsGenerated(), result.getBillsAudited(), result.getPaymentsProcessed(),
                result.getPaymentsSucceeded(), result.getPaymentsFailed());

            // 如果有错误信息，记录详细日志
            if (!result.getErrorMessages().isEmpty()) {
                log.warn("任务执行过程中出现错误，任务ID: {}, 错误信息: {}", taskId, result.getErrorMessages());
            }

        } catch (Exception e) {
            log.error("执行抄表任务异常，任务ID: {}", taskId, e);
            performanceMonitor.recordTaskComplete(taskIdStr, 0, meterNos.size());
            failCount.addAndGet(meterNos.size());
        }
    }

    /**
     * 获取抄表手册中的水表编号列表
     */
    private List<String> fetchMeterNos(Long meterBookId, Long taskId) {
        List<String> meterNos = waterfeeMeterService.getMeterNosByBookId(meterBookId);
        if (meterNos.isEmpty()) {
            log.warn("抄表手册中没有水表，任务ID: {}", taskId);
        }
        return meterNos;
    }

    /**
     * 更新任务状态（独立事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateTaskStatus(WaterfeeReadingTask task, Date now) {
        task.setLastExecuteTime(now);
        task.setNextExecuteTime(calculateNextExecuteTime(task));
        readingTaskMapper.updateById(task);
    }

    /**
     * 重置性能指标
     */
    private void resetPerformanceMetrics() {
        totalProcessed.set(0);
        successCount.set(0);
        failCount.set(0);
        performanceMetrics.clear();
    }

    /**
     * 创建任务执行性能指标
     */
    private TaskExecutionMetrics createTaskExecutionMetrics(long totalDuration, int taskCount, int successCount, int failCount) {
        MeterReadingPerformanceMonitor.PerformanceReport report = performanceMonitor.getPerformanceReport();

        // 计算基础统计信息
        double avgTaskDuration = taskCount > 0 ? (double) totalDuration / taskCount : 0;
        double successRate = (successCount + failCount) > 0 ? (successCount * 100.0) / (successCount + failCount) : 0;
        double processingSpeed = totalDuration > 0 ? (totalProcessed.get() * 1000.0) / totalDuration : 0;

        // 执行时间信息
        TaskExecutionMetrics.ExecutionTimeInfo executionTime = TaskExecutionMetrics.ExecutionTimeInfo.builder()
            .startTime(LocalDateTime.now().minusNanos(totalDuration * 1_000_000))
            .endTime(LocalDateTime.now())
            .totalDurationMs(totalDuration)
            .totalDurationSeconds(totalDuration / 1000.0)
            .averageTaskDurationMs(avgTaskDuration)
            .build();

        // 任务处理统计
        TaskExecutionMetrics.TaskProcessingStats taskStats = TaskExecutionMetrics.TaskProcessingStats.builder()
            .totalTasks(taskCount)
            .successfulTasks(successCount)
            .failedTasks(failCount)
            .successRate(successRate)
            .activeTasks(performanceMonitor.getActiveTaskCount())
            .build();

        // 水表处理统计
        TaskExecutionMetrics.MeterProcessingStats meterStats = TaskExecutionMetrics.MeterProcessingStats.builder()
            .totalMetersProcessed(totalProcessed.get())
            .processingSpeedPerSecond(processingSpeed)
            .averageMetersPerTask(taskCount > 0 ? totalProcessed.get() / taskCount : 0)
            .averageProcessingTimePerMeter(totalProcessed.get() > 0 ? (double) totalDuration / totalProcessed.get() : 0)
            .build();

        // 账单处理统计
        TaskExecutionMetrics.BillingStats billingStats = TaskExecutionMetrics.BillingStats.builder()
            .billsGenerated(report.getTotalBillsGenerated())
            .billsAudited(report.getTotalBillsAudited())
            .billAuditRate(report.getBillAuditRate())
            .paymentsProcessed(report.getTotalPaymentsProcessed())
            .paymentsSucceeded(report.getTotalPaymentsSucceeded())
            .paymentsFailed(report.getTotalPaymentsFailed())
            .paymentSuccessRate(report.getPaymentSuccessRate())
            .build();

        // 系统累计统计
        TaskExecutionMetrics.SystemStats systemStats = TaskExecutionMetrics.SystemStats.builder()
            .totalTasksExecuted(report.getTotalTasksExecuted())
            .totalMetersProcessed(report.getTotalMetersProcessed())
            .overallSuccessRate(report.getOverallSuccessRate())
            .averageTaskDuration(report.getAverageTaskDuration())
            .averageProcessingTimePerMeter(report.getAverageProcessingTimePerMeter())
            .totalBillsGenerated(report.getTotalBillsGenerated())
            .totalPaymentsSucceeded(report.getTotalPaymentsSucceeded())
            .build();

        // 性能分析
        TaskExecutionMetrics.PerformanceAnalysis performanceAnalysis = createPerformanceAnalysis(
            successRate, processingSpeed, report);

        return TaskExecutionMetrics.builder()
            .executionTime(executionTime)
            .taskStats(taskStats)
            .meterStats(meterStats)
            .billingStats(billingStats)
            .systemStats(systemStats)
            .performanceAnalysis(performanceAnalysis)
            .build();
    }

    /**
     * 写入性能报告到文件
     */
    private void writePerformanceReportToFile(TaskExecutionMetrics metrics) {
        try {
            // 确保目录存在
            ensureDirectoryExists(PERFORMANCE_REPORTS_DIR);

            // 生成文件名
            LocalDateTime now = LocalDateTime.now();
            String fileName = String.format("performance-report_%s.txt", now.format(FILE_DATETIME_FORMAT));
            Path filePath = Paths.get(PERFORMANCE_REPORTS_DIR, fileName);

            // 写入详细的性能报告
            try (BufferedWriter writer = Files.newBufferedWriter(filePath)) {
                writeDetailedPerformanceReport(writer, metrics, now);
            }

            log.info("性能报告已写入文件: {}", filePath.toAbsolutePath());

        } catch (IOException e) {
            log.error("写入性能报告文件失败", e);
        }
    }

    /**
     * 写入详细的性能报告内容
     */
    private void writeDetailedPerformanceReport(BufferedWriter writer, TaskExecutionMetrics metrics, LocalDateTime timestamp) throws IOException {

        // 写入报告头部
        writer.write("=".repeat(100));
        writer.newLine();
        writer.write("                              定时任务执行性能指标报告");
        writer.newLine();
        writer.write("                                  " + timestamp.format(REPORT_DATETIME_FORMAT));
        writer.newLine();
        writer.write("=".repeat(100));
        writer.newLine();
        writer.write("┌─────────────────────────────────┬─────────────────────────────────────────────┐");
        writer.newLine();
        writer.write("│ 指标项                          │ 数值                                        │");
        writer.newLine();
        writer.write("├─────────────────────────────────┼─────────────────────────────────────────────┤");
        writer.newLine();

        // 执行时间信息
        if (metrics.getExecutionTime() != null) {
            TaskExecutionMetrics.ExecutionTimeInfo execTime = metrics.getExecutionTime();
            writer.write(String.format("│ 本次执行总耗时                  │ %s ms (%.2f 秒)                          │",
                String.format("%,d", execTime.getTotalDurationMs()), execTime.getTotalDurationSeconds()));
            writer.newLine();
            writer.write(String.format("│ 平均任务耗时                    │ %.2f ms                                   │", execTime.getAverageTaskDurationMs()));
            writer.newLine();
        }

        // 任务处理统计
        if (metrics.getTaskStats() != null) {
            TaskExecutionMetrics.TaskProcessingStats taskStats = metrics.getTaskStats();
            writer.write(String.format("│ 处理任务数量                    │ %d 个                                       │", taskStats.getTotalTasks()));
            writer.newLine();
            writer.write(String.format("│ 任务成功数量                    │ %d 个                                       │", taskStats.getSuccessfulTasks()));
            writer.newLine();
            writer.write(String.format("│ 任务失败数量                    │ %d 个                                       │", taskStats.getFailedTasks()));
            writer.newLine();
            writer.write(String.format("│ 任务成功率                      │ %.2f%%                                     │", taskStats.getSuccessRate()));
            writer.newLine();
            writer.write(String.format("│ 当前活跃任务数                  │ %d 个                                       │", taskStats.getActiveTasks()));
            writer.newLine();
        }

        writer.write("├─────────────────────────────────┼─────────────────────────────────────────────┤");
        writer.newLine();

        // 水表处理统计
        if (metrics.getMeterStats() != null) {
            TaskExecutionMetrics.MeterProcessingStats meterStats = metrics.getMeterStats();
            writer.write(String.format("│ 处理水表总数                    │ %d 个                                       │", meterStats.getTotalMetersProcessed()));
            writer.newLine();
            writer.write(String.format("│ 水表处理速度                    │ %.2f 个/秒                                │", meterStats.getProcessingSpeedPerSecond()));
            writer.newLine();
            writer.write(String.format("│ 平均每任务处理水表数            │ %d 个                                       │", meterStats.getAverageMetersPerTask()));
            writer.newLine();
            writer.write(String.format("│ 平均每表处理时间                │ %.2f ms                                   │", meterStats.getAverageProcessingTimePerMeter()));
            writer.newLine();
        }

        writer.write("├─────────────────────────────────┼─────────────────────────────────────────────┤");
        writer.newLine();

        // 账单处理统计
        if (metrics.getBillingStats() != null) {
            TaskExecutionMetrics.BillingStats billingStats = metrics.getBillingStats();
            writer.write(String.format("│ 累计生成账单数                  │ %d 个                                       │", billingStats.getBillsGenerated()));
            writer.newLine();
            writer.write(String.format("│ 累计审核账单数                  │ %d 个                                       │", billingStats.getBillsAudited()));
            writer.newLine();
            writer.write(String.format("│ 账单审核率                      │ %.2f%%                                     │", billingStats.getBillAuditRate()));
            writer.newLine();
            writer.write(String.format("│ 处理支付数量                    │ %d 个                                       │", billingStats.getPaymentsProcessed()));
            writer.newLine();
            writer.write(String.format("│ 支付成功数量                    │ %d 个                                       │", billingStats.getPaymentsSucceeded()));
            writer.newLine();
            writer.write(String.format("│ 支付失败数量                    │ %d 个                                       │", billingStats.getPaymentsFailed()));
            writer.newLine();
            writer.write(String.format("│ 支付成功率                      │ %.2f%%                                     │", billingStats.getPaymentSuccessRate()));
            writer.newLine();
        }

        writer.write("├─────────────────────────────────┼─────────────────────────────────────────────┤");
        writer.newLine();

        // 系统累计统计
        if (metrics.getSystemStats() != null) {
            TaskExecutionMetrics.SystemStats systemStats = metrics.getSystemStats();
            writer.write(String.format("│ 系统累计执行任务数              │ %d 个                                       │", systemStats.getTotalTasksExecuted()));
            writer.newLine();
            writer.write(String.format("│ 系统累计处理水表数              │ %d 个                                       │", systemStats.getTotalMetersProcessed()));
            writer.newLine();
            writer.write(String.format("│ 系统整体成功率                  │ %.2f%%                                     │", systemStats.getOverallSuccessRate()));
            writer.newLine();
            writer.write(String.format("│ 系统平均任务耗时                │ %d ms                                       │", systemStats.getAverageTaskDuration()));
            writer.newLine();
            writer.write(String.format("│ 系统平均每表处理时间            │ %d ms                                       │", systemStats.getAverageProcessingTimePerMeter()));
            writer.newLine();
        }

        writer.write("└─────────────────────────────────┴─────────────────────────────────────────────┘");
        writer.newLine();

        // 写入性能分析
        if (metrics.getPerformanceAnalysis() != null) {
            writePerformanceAnalysis(writer, metrics.getPerformanceAnalysis());
        }

        writer.write("=".repeat(100));
        writer.newLine();
    }

    /**
     * 写入性能分析到文件
     */
    private void writePerformanceAnalysis(BufferedWriter writer, TaskExecutionMetrics.PerformanceAnalysis analysis) throws IOException {
        writer.newLine();
        writer.write("📊 性能分析报告:");
        writer.newLine();
        writer.write(String.format("   等级: %s (得分: %.1f/100)", analysis.getPerformanceLevel().toUpperCase(), analysis.getPerformanceScore()));
        writer.newLine();

        if (!analysis.getStrengths().isEmpty()) {
            writer.write("   ✅ 优势:");
            writer.newLine();
            for (String strength : analysis.getStrengths()) {
                writer.write("      • " + strength);
                writer.newLine();
            }
        }

        if (!analysis.getWeaknesses().isEmpty()) {
            writer.write("   ⚠️  劣势:");
            writer.newLine();
            for (String weakness : analysis.getWeaknesses()) {
                writer.write("      • " + weakness);
                writer.newLine();
            }
        }

        if (!analysis.getRecommendations().isEmpty()) {
            writer.write("   💡 建议:");
            writer.newLine();
            for (String recommendation : analysis.getRecommendations()) {
                writer.write("      • " + recommendation);
                writer.newLine();
            }
        }

        if (analysis.getTrendAnalysis() != null) {
            TaskExecutionMetrics.TrendAnalysis trend = analysis.getTrendAnalysis();
            writer.write(String.format("   📈 趋势: %s (%s)", trend.getTrendDirection().toUpperCase(), trend.getTrendDescription()));
            writer.newLine();
        }
    }

    /**
     * 写入JSON格式的性能指标到文件
     */
    private void writeJsonMetricsToFile(TaskExecutionMetrics metrics) {
        try {
            // 确保目录存在
            ensureDirectoryExists(JSON_REPORTS_DIR);

            // 生成文件名
            LocalDateTime now = LocalDateTime.now();
            String fileName = String.format("metrics_%s.json", now.format(FILE_DATETIME_FORMAT));
            Path filePath = Paths.get(JSON_REPORTS_DIR, fileName);

            // 写入JSON格式的性能指标
            try (BufferedWriter writer = Files.newBufferedWriter(filePath)) {
                writeJsonMetrics(writer, metrics, now);
            }

            log.info("JSON性能指标已写入文件: {}", filePath.toAbsolutePath());

        } catch (IOException e) {
            log.error("写入JSON性能指标文件失败", e);
        }
    }

    /**
     * 写入JSON格式的性能指标内容
     */
    private void writeJsonMetrics(BufferedWriter writer, TaskExecutionMetrics metrics, LocalDateTime timestamp) throws IOException {
        writer.write("{");
        writer.newLine();
        writer.write(String.format("  \"timestamp\": \"%s\",", timestamp.format(REPORT_DATETIME_FORMAT)));
        writer.newLine();
        writer.write(String.format("  \"healthStatus\": \"%s\",", metrics.getHealthStatus()));
        writer.newLine();
        writer.write(String.format("  \"performanceSummary\": \"%s\",", metrics.getPerformanceSummary()));
        writer.newLine();

        if (metrics.getTaskStats() != null) {
            writer.write("  \"taskStats\": {");
            writer.newLine();
            writer.write(String.format("    \"totalTasks\": %d,", metrics.getTaskStats().getTotalTasks()));
            writer.newLine();
            writer.write(String.format("    \"successfulTasks\": %d,", metrics.getTaskStats().getSuccessfulTasks()));
            writer.newLine();
            writer.write(String.format("    \"failedTasks\": %d,", metrics.getTaskStats().getFailedTasks()));
            writer.newLine();
            writer.write(String.format("    \"successRate\": %.2f,", metrics.getTaskStats().getSuccessRate()));
            writer.newLine();
            writer.write(String.format("    \"activeTasks\": %d", metrics.getTaskStats().getActiveTasks()));
            writer.newLine();
            writer.write("  },");
            writer.newLine();
        }

        if (metrics.getMeterStats() != null) {
            writer.write("  \"meterStats\": {");
            writer.newLine();
            writer.write(String.format("    \"totalProcessed\": %d,", metrics.getMeterStats().getTotalMetersProcessed()));
            writer.newLine();
            writer.write(String.format("    \"processingSpeed\": %.2f,", metrics.getMeterStats().getProcessingSpeedPerSecond()));
            writer.newLine();
            writer.write(String.format("    \"averageMetersPerTask\": %d,", metrics.getMeterStats().getAverageMetersPerTask()));
            writer.newLine();
            writer.write(String.format("    \"averageProcessingTimePerMeter\": %.2f", metrics.getMeterStats().getAverageProcessingTimePerMeter()));
            writer.newLine();
            writer.write("  },");
            writer.newLine();
        }

        if (metrics.getBillingStats() != null) {
            writer.write("  \"billingStats\": {");
            writer.newLine();
            writer.write(String.format("    \"billsGenerated\": %d,", metrics.getBillingStats().getBillsGenerated()));
            writer.newLine();
            writer.write(String.format("    \"billsAudited\": %d,", metrics.getBillingStats().getBillsAudited()));
            writer.newLine();
            writer.write(String.format("    \"billAuditRate\": %.2f,", metrics.getBillingStats().getBillAuditRate()));
            writer.newLine();
            writer.write(String.format("    \"paymentsProcessed\": %d,", metrics.getBillingStats().getPaymentsProcessed()));
            writer.newLine();
            writer.write(String.format("    \"paymentsSucceeded\": %d,", metrics.getBillingStats().getPaymentsSucceeded()));
            writer.newLine();
            writer.write(String.format("    \"paymentsFailed\": %d,", metrics.getBillingStats().getPaymentsFailed()));
            writer.newLine();
            writer.write(String.format("    \"paymentSuccessRate\": %.2f", metrics.getBillingStats().getPaymentSuccessRate()));
            writer.newLine();
            writer.write("  },");
            writer.newLine();
        }

        if (metrics.getPerformanceAnalysis() != null) {
            writer.write("  \"performanceAnalysis\": {");
            writer.newLine();
            writer.write(String.format("    \"performanceLevel\": \"%s\",", metrics.getPerformanceAnalysis().getPerformanceLevel()));
            writer.newLine();
            writer.write(String.format("    \"performanceScore\": %.1f,", metrics.getPerformanceAnalysis().getPerformanceScore()));
            writer.newLine();
            writer.write("    \"strengths\": [");
            if (!metrics.getPerformanceAnalysis().getStrengths().isEmpty()) {
                writer.newLine();
                for (int i = 0; i < metrics.getPerformanceAnalysis().getStrengths().size(); i++) {
                    writer.write(String.format("      \"%s\"", metrics.getPerformanceAnalysis().getStrengths().get(i)));
                    if (i < metrics.getPerformanceAnalysis().getStrengths().size() - 1) {
                        writer.write(",");
                    }
                    writer.newLine();
                }
                writer.write("    ");
            }
            writer.write("],");
            writer.newLine();
            writer.write("    \"recommendations\": [");
            if (!metrics.getPerformanceAnalysis().getRecommendations().isEmpty()) {
                writer.newLine();
                for (int i = 0; i < metrics.getPerformanceAnalysis().getRecommendations().size(); i++) {
                    writer.write(String.format("      \"%s\"", metrics.getPerformanceAnalysis().getRecommendations().get(i)));
                    if (i < metrics.getPerformanceAnalysis().getRecommendations().size() - 1) {
                        writer.write(",");
                    }
                    writer.newLine();
                }
                writer.write("    ");
            }
            writer.write("]");
            writer.newLine();
            writer.write("  }");
            writer.newLine();
        }

        writer.write("}");
        writer.newLine();
    }

    /**
     * 计算下次执行时间（按自定义季度规则）
     *
     * @param task 抄表任务
     * @return 下次执行时间
     */
    private Date calculateNextExecuteTime(WaterfeeReadingTask task) {
        Integer readingDay = task.getReadingDay();
        if (readingDay == null || readingDay < 1) {
            readingDay = 1;
        }

        Date baseDate = task.getLastExecuteTime() != null ? task.getLastExecuteTime() : new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(baseDate);

        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;

        Calendar nextQuarterStart = Calendar.getInstance();

        // 判断当前处于哪个自定义季度，并确定下季度起始时间
        if (month == 12 || month == 1 || month == 2) {
            // 当前为第4季度，下季度为第1季度（3月1日）
            if (month == 12) {
                year += 1;
            }
            nextQuarterStart.set(year, Calendar.MARCH, 1, 1, 0, 0);
        } else if (month <= 5) {
            // 当前为第1季度，下季度为第2季度（6月1日）
            nextQuarterStart.set(year, Calendar.JUNE, 1, 1, 0, 0);
        } else if (month <= 8) {
            // 当前为第2季度，下季度为第3季度（9月1日）
            nextQuarterStart.set(year, Calendar.SEPTEMBER, 1, 1, 0, 0);
        } else {
            // 当前为第3季度，下季度为第4季度（12月1日）
            nextQuarterStart.set(year, Calendar.DECEMBER, 1, 1, 0, 0);
        }

        // 设置为抄表例日（不能超过月底）
        int maxDay = nextQuarterStart.getActualMaximum(Calendar.DAY_OF_MONTH);
        nextQuarterStart.set(Calendar.DAY_OF_MONTH, Math.min(readingDay, maxDay));

        // 设置时间为 1:00:00
        nextQuarterStart.set(Calendar.HOUR_OF_DAY, 1);
        nextQuarterStart.set(Calendar.MINUTE, 0);
        nextQuarterStart.set(Calendar.SECOND, 0);
        nextQuarterStart.set(Calendar.MILLISECOND, 0);

        return nextQuarterStart.getTime();
    }

    /**
     * 手动触发定时任务（仅用于测试）
     */
    @GetMapping("/test")
    public void manualTrigger() {
        checkAndExecuteReadingTasks();
    }

    /**
     * 创建性能分析
     */
    private TaskExecutionMetrics.PerformanceAnalysis createPerformanceAnalysis(
        double successRate, double processingSpeed, MeterReadingPerformanceMonitor.PerformanceReport report) {

        // 计算性能得分 (0-100)
        double performanceScore = calculatePerformanceScore(successRate, processingSpeed, report);

        // 确定性能等级
        String performanceLevel = determinePerformanceLevel(performanceScore);

        // 分析优势和劣势
        List<String> strengths = analyzeStrengths(successRate, processingSpeed, report);
        List<String> weaknesses = analyzeWeaknesses(successRate, processingSpeed, report);

        // 生成建议
        List<String> recommendations = generateRecommendations(successRate, processingSpeed, report);

        // 趋势分析
        TaskExecutionMetrics.TrendAnalysis trendAnalysis = analyzeTrendForMetrics(report);

        return TaskExecutionMetrics.PerformanceAnalysis.builder()
            .performanceLevel(performanceLevel)
            .performanceScore(performanceScore)
            .strengths(strengths)
            .weaknesses(weaknesses)
            .recommendations(recommendations)
            .trendAnalysis(trendAnalysis)
            .build();
    }

    /**
     * 计算性能得分
     */
    private double calculatePerformanceScore(double successRate, double processingSpeed,
                                             MeterReadingPerformanceMonitor.PerformanceReport report) {
        double score = 0;

        // 成功率权重 40%
        score += (successRate / 100.0) * 40;

        // 处理速度权重 30% (以100个/秒为满分基准)
        score += Math.min(processingSpeed / 100.0, 1.0) * 30;

        // 支付成功率权重 20%
        if (report.getTotalPaymentsProcessed() > 0) {
            score += (report.getPaymentSuccessRate() / 100.0) * 20;
        } else {
            score += 20; // 如果没有支付处理，给满分
        }

        // 系统稳定性权重 10%
        double stabilityScore = report.getOverallSuccessRate() / 100.0;
        score += stabilityScore * 10;

        return Math.min(score, 100.0);
    }

    /**
     * 确定性能等级
     */
    private String determinePerformanceLevel(double score) {
        if (score >= 90) return "excellent";
        if (score >= 75) return "good";
        if (score >= 60) return "normal";
        return "poor";
    }

    /**
     * 分析优势
     */
    private List<String> analyzeStrengths(double successRate, double processingSpeed,
                                          MeterReadingPerformanceMonitor.PerformanceReport report) {
        List<String> strengths = new ArrayList<>();

        if (successRate >= 98) {
            strengths.add("任务成功率极高(" + String.format("%.1f%%", successRate) + ")");
        }

        if (processingSpeed >= 200) {
            strengths.add("处理速度很快(" + String.format("%.1f个/秒", processingSpeed) + ")");
        }

        if (report.getPaymentSuccessRate() >= 95 && report.getTotalPaymentsProcessed() > 0) {
            strengths.add("支付成功率很高(" + String.format("%.1f%%", report.getPaymentSuccessRate()) + ")");
        }

        if (report.getOverallSuccessRate() >= 95) {
            strengths.add("系统整体稳定性良好");
        }

        return strengths;
    }

    /**
     * 分析劣势
     */
    private List<String> analyzeWeaknesses(double successRate, double processingSpeed,
                                           MeterReadingPerformanceMonitor.PerformanceReport report) {
        List<String> weaknesses = new ArrayList<>();

        if (successRate < 90) {
            weaknesses.add("任务成功率偏低(" + String.format("%.1f%%", successRate) + ")");
        }

        if (processingSpeed < 50) {
            weaknesses.add("处理速度较慢(" + String.format("%.1f个/秒", processingSpeed) + ")");
        }

        if (report.getPaymentSuccessRate() < 85 && report.getTotalPaymentsProcessed() > 0) {
            weaknesses.add("支付成功率偏低(" + String.format("%.1f%%", report.getPaymentSuccessRate()) + ")");
        }

        if (performanceMonitor.getActiveTaskCount() > MAX_CONCURRENT_TASKS * 0.8) {
            weaknesses.add("系统负载较高");
        }

        return weaknesses;
    }

    /**
     * 生成优化建议
     */
    private List<String> generateRecommendations(double successRate, double processingSpeed,
                                                 MeterReadingPerformanceMonitor.PerformanceReport report) {
        List<String> recommendations = new ArrayList<>();

        if (successRate < 95) {
            recommendations.add("检查错误日志，优化异常处理机制");
        }

        if (processingSpeed < 100) {
            recommendations.add("考虑增加并发数或优化批处理大小");
        }

        if (report.getPaymentSuccessRate() < 90 && report.getTotalPaymentsProcessed() > 0) {
            recommendations.add("检查客户余额状态和支付接口稳定性");
        }

        if (report.getBillAuditRate() < 95 && report.getTotalBillsGenerated() > 0) {
            recommendations.add("优化账单审核逻辑，提高审核成功率");
        }

        return recommendations;
    }

    /**
     * 趋势分析（用于性能指标）
     */
    private TaskExecutionMetrics.TrendAnalysis analyzeTrendForMetrics(MeterReadingPerformanceMonitor.PerformanceReport report) {
        long avgDuration = report.getAverageTaskDuration();
        long recentAvgDuration = report.getRecentAverageTaskDuration();

        double changePercent = 0;
        String direction = "stable";
        String description = "性能表现稳定";

        if (avgDuration > 0 && recentAvgDuration > 0) {
            changePercent = ((double) recentAvgDuration - avgDuration) / avgDuration * 100;

            if (changePercent > 5) {
                direction = "declining";
                description = "最近性能有所下降，建议关注";
            } else if (changePercent < -5) {
                direction = "improving";
                description = "最近性能持续改善";
            }
        }

        return TaskExecutionMetrics.TrendAnalysis.builder()
            .performanceChangePercent(changePercent)
            .trendDirection(direction)
            .trendDescription(description)
            .build();
    }

    /**
     * 写入每日汇总报告
     */
    private void writeDailySummaryReport(TaskExecutionMetrics metrics) {
        try {
            // 确保目录存在
            ensureDirectoryExists(DAILY_REPORTS_DIR);

            // 生成文件名（按日期）
            LocalDateTime now = LocalDateTime.now();
            String fileName = String.format("daily-summary_%s.txt", now.format(FILE_DATE_FORMAT));
            Path filePath = Paths.get(DAILY_REPORTS_DIR, fileName);

            // 追加写入每日汇总
            try (BufferedWriter writer = Files.newBufferedWriter(filePath,
                java.nio.file.StandardOpenOption.CREATE,
                java.nio.file.StandardOpenOption.APPEND)) {

                writeDailySummaryContent(writer, metrics, now);
            }

            log.info("每日汇总报告已更新: {}", filePath.toAbsolutePath());

        } catch (IOException e) {
            log.error("写入每日汇总报告失败", e);
        }
    }

    /**
     * 写入每日汇总内容
     */
    private void writeDailySummaryContent(BufferedWriter writer, TaskExecutionMetrics metrics, LocalDateTime timestamp) throws IOException {
        writer.write("=".repeat(80));
        writer.newLine();
        writer.write(String.format("执行时间: %s", timestamp.format(REPORT_DATETIME_FORMAT)));
        writer.newLine();
        writer.write("=".repeat(80));
        writer.newLine();

        if (metrics.getTaskStats() != null) {
            TaskExecutionMetrics.TaskProcessingStats taskStats = metrics.getTaskStats();
            writer.write(String.format("任务统计: 总数=%d, 成功=%d, 失败=%d, 成功率=%.2f%%",
                taskStats.getTotalTasks(), taskStats.getSuccessfulTasks(),
                taskStats.getFailedTasks(), taskStats.getSuccessRate()));
            writer.newLine();
        }

        if (metrics.getMeterStats() != null) {
            TaskExecutionMetrics.MeterProcessingStats meterStats = metrics.getMeterStats();
            writer.write(String.format("水表处理: 总数=%d, 速度=%.2f个/秒, 平均耗时=%.2fms",
                meterStats.getTotalMetersProcessed(), meterStats.getProcessingSpeedPerSecond(),
                meterStats.getAverageProcessingTimePerMeter()));
            writer.newLine();
        }

        if (metrics.getBillingStats() != null) {
            TaskExecutionMetrics.BillingStats billingStats = metrics.getBillingStats();
            writer.write(String.format("账单处理: 生成=%d, 审核=%d, 支付成功=%d, 支付成功率=%.2f%%",
                billingStats.getBillsGenerated(), billingStats.getBillsAudited(),
                billingStats.getPaymentsSucceeded(), billingStats.getPaymentSuccessRate()));
            writer.newLine();
        }

        if (metrics.getPerformanceAnalysis() != null) {
            TaskExecutionMetrics.PerformanceAnalysis analysis = metrics.getPerformanceAnalysis();
            writer.write(String.format("性能评估: 等级=%s, 得分=%.1f, 健康状态=%s",
                analysis.getPerformanceLevel().toUpperCase(), analysis.getPerformanceScore(),
                metrics.getHealthStatus()));
            writer.newLine();
        }

        writer.newLine();
    }

    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String dirPath) throws IOException {
        Path path = Paths.get(dirPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            log.info("创建目录: {}", path.toAbsolutePath());
        }
    }

}

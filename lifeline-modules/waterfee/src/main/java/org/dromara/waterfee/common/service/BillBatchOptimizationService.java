package org.dromara.waterfee.common.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.bill.domain.WaterfeeBill;
import org.dromara.waterfee.bill.mapper.WaterfeeBillMapper;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.common.cache.BillPerformanceCacheService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 账单批量操作优化服务
 * 提供高效的批量账单处理功能
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillBatchOptimizationService {

    private final WaterfeeBillMapper billMapper;
    private final IWaterfeeBillService billService;
    private final BillPerformanceCacheService cacheService;

    /**
     * 批量更新账单状态（优化版本）
     *
     * @param billIds 账单ID集合
     * @param newStatus 新状态
     * @param updateBy 更新人
     * @return 更新成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateBillStatus(Collection<Long> billIds, String newStatus, Long updateBy) {
        if (billIds == null || billIds.isEmpty()) {
            return 0;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用批量更新SQL，避免循环单条更新
            int updateCount = billMapper.batchUpdateBillStatus(billIds, newStatus, updateBy, new Date());

            long endTime = System.currentTimeMillis();
            log.info("批量更新账单状态完成，数量: {}, 耗时: {}ms", updateCount, endTime - startTime);

            return updateCount;
        } catch (Exception e) {
            log.error("批量更新账单状态失败", e);
            throw e;
        }
    }

    /**
     * 批量查询账单信息（优化版本）
     *
     * @param billIds 账单ID集合
     * @return 账单信息列表
     */
    public List<WaterfeeBill> batchQueryBills(Collection<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return List.of();
        }

        // 生成批量查询缓存键
        String cacheKey = cacheService.generateBatchQueryKey("bills", billIds.toString());

        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<WaterfeeBill> cachedResult = cacheService.getCachedBatchQueryResult(cacheKey, List.class);
        if (cachedResult != null) {
            log.debug("从缓存获取批量账单查询结果，数量: {}", cachedResult.size());
            return cachedResult;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用批量查询，避免N+1查询问题
            List<WaterfeeBill> bills = billMapper.batchSelectBillsByIds(billIds);

            // 缓存查询结果
            cacheService.cacheBatchQueryResult(cacheKey, bills);

            long endTime = System.currentTimeMillis();
            log.info("批量查询账单完成，数量: {}, 耗时: {}ms", bills.size(), endTime - startTime);

            return bills;
        } catch (Exception e) {
            log.error("批量查询账单失败", e);
            throw e;
        }
    }

    /**
     * 批量获取账单详细信息（包含客户信息）
     *
     * @param billIds 账单ID集合
     * @return 账单详细信息Map
     */
    public List<Map<String, Object>> batchGetBillDetailsWithCustomer(Collection<Long> billIds) {
        if (billIds == null || billIds.isEmpty()) {
            return List.of();
        }

        // 生成批量查询缓存键
        String cacheKey = cacheService.generateBatchQueryKey("bill_details", billIds.toString());

        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> cachedResult = cacheService.getCachedBatchQueryResult(cacheKey, List.class);
        if (cachedResult != null) {
            log.debug("从缓存获取批量账单详情查询结果，数量: {}", cachedResult.size());
            return cachedResult;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用优化的批量查询SQL
            List<Map<String, Object>> billDetails = billMapper.batchGetBillDetailsWithCustomer(billIds);

            // 缓存查询结果
            cacheService.cacheBatchQueryResult(cacheKey, billDetails);

            long endTime = System.currentTimeMillis();
            log.info("批量获取账单详情完成，数量: {}, 耗时: {}ms", billDetails.size(), endTime - startTime);

            return billDetails;
        } catch (Exception e) {
            log.error("批量获取账单详情失败", e);
            throw e;
        }
    }

    /**
     * 批量发行账单（优化版本）
     *
     * @param meterBookIds 表册ID集合
     * @return 发行成功的账单数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchIssueBills(Collection<Long> meterBookIds) {
        if (meterBookIds == null || meterBookIds.isEmpty()) {
            return 0;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用批量更新SQL，提升性能
            int updateCount = billMapper.batchIssueBillsByMeterBookIds(meterBookIds, new Date());

            long endTime = System.currentTimeMillis();
            log.info("批量发行账单完成，表册数量: {}, 账单数量: {}, 耗时: {}ms",
                    meterBookIds.size(), updateCount, endTime - startTime);

            return updateCount;
        } catch (Exception e) {
            log.error("批量发行账单失败", e);
            throw e;
        }
    }

    /**
     * 批量计算账单统计信息
     *
     * @param customerIds 客户ID集合
     * @return 统计信息Map
     */
    public Map<Long, Map<String, Object>> batchCalculateBillStatistics(Collection<Long> customerIds) {
        if (customerIds == null || customerIds.isEmpty()) {
            return Map.of();
        }

        // 生成批量查询缓存键
        String cacheKey = cacheService.generateBatchQueryKey("bill_stats", customerIds.toString());

        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        Map<Long, Map<String, Object>> cachedResult = cacheService.getCachedBatchQueryResult(cacheKey, Map.class);
        if (cachedResult != null) {
            log.debug("从缓存获取批量账单统计结果，数量: {}", cachedResult.size());
            return cachedResult;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 使用优化的批量统计查询
            List<Map<String, Object>> statisticsList = billMapper.batchCalculateBillStatistics(customerIds);

            // 转换为Map结构
            Map<Long, Map<String, Object>> statisticsMap = statisticsList.stream()
                    .collect(Collectors.toMap(
                            stat -> (Long) stat.get("customer_id"),
                            stat -> stat
                    ));

            // 缓存查询结果
            cacheService.cacheBatchQueryResult(cacheKey, statisticsMap);

            long endTime = System.currentTimeMillis();
            log.info("批量计算账单统计完成，客户数量: {}, 耗时: {}ms",
                    customerIds.size(), endTime - startTime);

            return statisticsMap;
        } catch (Exception e) {
            log.error("批量计算账单统计失败", e);
            throw e;
        }
    }

    /**
     * 批量删除账单（优化版本）
     *
     * @param billIds 账单ID集合
     * @param isValid 是否进行有效性校验
     * @return 删除成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteBills(Collection<Long> billIds, boolean isValid) {
        if (billIds == null || billIds.isEmpty()) {
            return 0;
        }

        long startTime = System.currentTimeMillis();

        try {
            if (isValid) {
                // 批量校验账单状态
                int invalidCount = billMapper.countInvalidBillsForDeletion(billIds);
                if (invalidCount > 0) {
                    throw new RuntimeException("存在不能删除的账单，数量: " + invalidCount);
                }
            }

            // 使用批量删除（逻辑删除）
            int deleteCount = billMapper.batchLogicalDeleteBills(billIds, new Date());

            long endTime = System.currentTimeMillis();
            log.info("批量删除账单完成，数量: {}, 耗时: {}ms", deleteCount, endTime - startTime);

            return deleteCount;
        } catch (Exception e) {
            log.error("批量删除账单失败", e);
            throw e;
        }
    }

    /**
     * 清理批量查询缓存
     */
    public void clearBatchQueryCache() {
        try {
            // 清理所有批量查询相关的缓存
            cacheService.evictAllBatchQueryCache();
            log.info("清理批量查询缓存完成");
        } catch (Exception e) {
            log.error("清理批量查询缓存失败", e);
        }
    }

    /**
     * 获取批量操作性能统计
     */
    public Map<String, Object> getBatchOperationStats() {
        Map<String, Object> stats = Map.of(
                "cacheStats", cacheService.getCacheStats(),
                "timestamp", new Date()
        );
        return stats;
    }
}

package org.dromara.waterfee.common.parallel;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;

/**
 * 并行处理引擎
 * 提供高效的并行处理能力，支持批量数据处理、任务分片、结果聚合等功能
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
public class ParallelProcessingEngine {

    @Value("${thread-pool.core-pool-size:10}")
    private int corePoolSize;

    @Value("${thread-pool.max-pool-size:50}")
    private int maxPoolSize;

    @Value("${thread-pool.queue-capacity:200}")
    private int queueCapacity;

    @Value("${batch-processing.batch-size:500}")
    private int defaultBatchSize;

    @Value("${batch-processing.timeout:30000}")
    private long defaultTimeoutMs;

    private ThreadPoolExecutor executor;
    private ScheduledExecutorService scheduledExecutor;

    // 处理统计
    private final Map<String, ProcessingStats> processingStats = new ConcurrentHashMap<>();

    /**
     * 处理统计信息
     */
    public static class ProcessingStats {
        // Getters
        @Getter
        private long totalTasks = 0;
        @Getter
        private long completedTasks = 0;
        @Getter
        private long failedTasks = 0;
        @Getter
        private long totalProcessingTime = 0;
        @Getter
        private long maxProcessingTime = 0;
        private long minProcessingTime = Long.MAX_VALUE;

        public synchronized void recordTask(long processingTime, boolean success) {
            totalTasks++;
            if (success) {
                completedTasks++;
            } else {
                failedTasks++;
            }

            totalProcessingTime += processingTime;
            maxProcessingTime = Math.max(maxProcessingTime, processingTime);
            minProcessingTime = Math.min(minProcessingTime, processingTime);
        }

        public double getSuccessRate() {
            return totalTasks > 0 ? (double) completedTasks / totalTasks : 0.0;
        }

        public double getAverageProcessingTime() {
            return completedTasks > 0 ? (double) totalProcessingTime / completedTasks : 0.0;
        }

        public long getMinProcessingTime() {
            return minProcessingTime == Long.MAX_VALUE ? 0 : minProcessingTime;
        }
    }

    /**
     * 处理结果
     */
    public static class ProcessingResult<T> {
        private final List<T> successResults;
        private final List<Exception> errors;
        @Getter
        private final long totalProcessingTime;
        @Getter
        private final int totalTasks;

        public ProcessingResult(List<T> successResults, List<Exception> errors,
                                long totalProcessingTime, int totalTasks) {
            this.successResults = new ArrayList<>(successResults);
            this.errors = new ArrayList<>(errors);
            this.totalProcessingTime = totalProcessingTime;
            this.totalTasks = totalTasks;
        }

        public boolean isAllSuccess() {
            return errors.isEmpty();
        }

        public double getSuccessRate() {
            return totalTasks > 0 ? (double) successResults.size() / totalTasks : 0.0;
        }

        // Getters
        public List<T> getSuccessResults() {
            return new ArrayList<>(successResults);
        }

        public List<Exception> getErrors() {
            return new ArrayList<>(errors);
        }

        public int getSuccessCount() {
            return successResults.size();
        }

        public int getErrorCount() {
            return errors.size();
        }
    }

    @PostConstruct
    public void init() {
        // 创建线程池
        executor = new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            new ThreadFactory() {
                private int counter = 0;

                @Override
                public Thread newThread(@NotNull Runnable r) {
                    Thread thread = new Thread(r, "parallel-processing-" + (++counter));
                    thread.setDaemon(false);
                    return thread;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 创建调度线程池
        scheduledExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "parallel-processing-scheduler");
            thread.setDaemon(true);
            return thread;
        });

        // 定期输出统计信息
        scheduledExecutor.scheduleAtFixedRate(this::logStatistics, 5, 5, TimeUnit.MINUTES);

        log.info("并行处理引擎初始化完成，核心线程数: {}, 最大线程数: {}, 队列容量: {}",
            corePoolSize, maxPoolSize, queueCapacity);
    }

    @PreDestroy
    public void destroy() {
        if (executor != null) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
        }

        log.info("并行处理引擎已关闭");
    }

    /**
     * 并行处理集合数据
     */
    public <T, R> ProcessingResult<R> processInParallel(String taskType, Collection<T> data,
                                                        Function<T, R> processor) {
        return processInParallel(taskType, data, processor, defaultBatchSize, defaultTimeoutMs);
    }

    /**
     * 并行处理集合数据（指定批次大小和超时时间）
     */
    public <T, R> ProcessingResult<R> processInParallel(String taskType, Collection<T> data,
                                                        Function<T, R> processor,
                                                        int batchSize, long timeoutMs) {
        if (data == null || data.isEmpty()) {
            return new ProcessingResult<>(Collections.emptyList(), Collections.emptyList(), 0, 0);
        }

        long startTime = System.currentTimeMillis();
        log.info("开始并行处理任务: {}, 数据量: {}, 批次大小: {}", taskType, data.size(), batchSize);

        // 分批处理
        List<List<T>> batches = partitionData(new ArrayList<>(data), batchSize);
        List<CompletableFuture<BatchResult<R>>> futures = new ArrayList<>();

        // 提交批次任务
        for (int i = 0; i < batches.size(); i++) {
            final int batchIndex = i;
            final List<T> batch = batches.get(i);

            CompletableFuture<BatchResult<R>> future = CompletableFuture.supplyAsync(() -> {
                return processBatch(taskType, batchIndex, batch, processor);
            }, executor);

            futures.add(future);
        }

        // 等待所有批次完成
        List<BatchResult<R>> batchResults = new ArrayList<>();
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

            allFutures.get(timeoutMs, TimeUnit.MILLISECONDS);

            // 收集结果
            for (CompletableFuture<BatchResult<R>> future : futures) {
                batchResults.add(future.get());
            }

        } catch (TimeoutException e) {
            log.warn("并行处理任务超时: {}, 超时时间: {}ms", taskType, timeoutMs);
            // 取消未完成的任务
            futures.forEach(f -> f.cancel(true));

            // 收集已完成的结果
            for (CompletableFuture<BatchResult<R>> future : futures) {
                if (future.isDone() && !future.isCancelled()) {
                    try {
                        batchResults.add(future.get());
                    } catch (Exception ex) {
                        log.warn("获取批次结果失败", ex);
                    }
                }
            }
        } catch (Exception e) {
            log.error("并行处理任务失败: {}", taskType, e);
        }

        // 聚合结果
        List<R> successResults = new ArrayList<>();
        List<Exception> errors = new ArrayList<>();

        for (BatchResult<R> batchResult : batchResults) {
            successResults.addAll(batchResult.getSuccessResults());
            errors.addAll(batchResult.getErrors());
        }

        long totalTime = System.currentTimeMillis() - startTime;

        // 记录统计信息
        ProcessingStats stats = processingStats.computeIfAbsent(taskType, k -> new ProcessingStats());
        stats.recordTask(totalTime, errors.isEmpty());

        log.info("并行处理任务完成: {}, 总耗时: {}ms, 成功: {}, 失败: {}",
            taskType, totalTime, successResults.size(), errors.size());

        return new ProcessingResult<>(successResults, errors, totalTime, data.size());
    }

    /**
     * 处理单个批次
     */
    private <T, R> BatchResult<R> processBatch(String taskType, int batchIndex,
                                               List<T> batch, Function<T, R> processor) {
        long startTime = System.currentTimeMillis();
        List<R> successResults = new ArrayList<>();
        List<Exception> errors = new ArrayList<>();

        log.debug("开始处理批次: {}-{}, 数据量: {}", taskType, batchIndex, batch.size());

        for (T item : batch) {
            try {
                R result = processor.apply(item);
                if (result != null) {
                    successResults.add(result);
                }
            } catch (Exception e) {
                errors.add(e);
                log.warn("处理单项数据失败: {}-{}", taskType, batchIndex, e);
            }
        }

        long processingTime = System.currentTimeMillis() - startTime;
        log.debug("批次处理完成: {}-{}, 耗时: {}ms, 成功: {}, 失败: {}",
            taskType, batchIndex, processingTime, successResults.size(), errors.size());

        return new BatchResult<>(successResults, errors, processingTime);
    }

    /**
     * 批次结果
     */
    @Getter
    private static class BatchResult<T> {
        private final List<T> successResults;
        private final List<Exception> errors;
        private final long processingTime;

        public BatchResult(List<T> successResults, List<Exception> errors, long processingTime) {
            this.successResults = new ArrayList<>(successResults);
            this.errors = new ArrayList<>(errors);
            this.processingTime = processingTime;
        }

    }

    /**
     * 数据分片
     */
    private <T> List<List<T>> partitionData(List<T> data, int batchSize) {
        List<List<T>> batches = new ArrayList<>();

        for (int i = 0; i < data.size(); i += batchSize) {
            int end = Math.min(i + batchSize, data.size());
            batches.add(data.subList(i, end));
        }

        return batches;
    }

    /**
     * 获取线程池状态
     */
    public Map<String, Object> getThreadPoolStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("corePoolSize", executor.getCorePoolSize());
        status.put("maximumPoolSize", executor.getMaximumPoolSize());
        status.put("activeCount", executor.getActiveCount());
        status.put("poolSize", executor.getPoolSize());
        status.put("queueSize", executor.getQueue().size());
        status.put("completedTaskCount", executor.getCompletedTaskCount());
        status.put("taskCount", executor.getTaskCount());
        return status;
    }

    /**
     * 获取处理统计信息
     */
    public Map<String, ProcessingStats> getProcessingStatistics() {
        return new HashMap<>(processingStats);
    }

    /**
     * 定期输出统计信息
     */
    private void logStatistics() {
        Map<String, Object> threadPoolStatus = getThreadPoolStatus();
        log.info("线程池状态: {}", threadPoolStatus);

        if (!processingStats.isEmpty()) {
            log.info("处理统计信息:");
            processingStats.forEach((taskType, stats) -> {
                log.info("  {}: 总任务={}, 成功={}, 失败={}, 成功率={}%, 平均耗时={}ms",
                    taskType, stats.getTotalTasks(), stats.getCompletedTasks(),
                    stats.getFailedTasks(), stats.getSuccessRate() * 100,
                    stats.getAverageProcessingTime());
            });
        }
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        processingStats.clear();
        log.info("处理统计信息已重置");
    }
}

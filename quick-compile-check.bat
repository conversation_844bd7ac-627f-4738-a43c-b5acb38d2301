@echo off
echo ========================================
echo 快速编译检查 - 性能优化模块
echo ========================================

cd lifeline-modules\waterfee

echo 检查关键类文件是否存在...
if exist "src\main\java\org\dromara\waterfee\common\cache\BillPerformanceCacheService.java" (
    echo ✓ BillPerformanceCacheService.java 存在
) else (
    echo ✗ BillPerformanceCacheService.java 不存在
)

if exist "src\main\java\org\dromara\waterfee\common\service\BillBatchOptimizationService.java" (
    echo ✓ BillBatchOptimizationService.java 存在
) else (
    echo ✗ BillBatchOptimizationService.java 不存在
)

if exist "src\main\resources\sql\performance-optimization-indexes.sql" (
    echo ✓ performance-optimization-indexes.sql 存在
) else (
    echo ✗ performance-optimization-indexes.sql 不存在
)

if exist "src\main\resources\application-performance.yml" (
    echo ✓ application-performance.yml 存在
) else (
    echo ✗ application-performance.yml 不存在
)

echo.
echo 尝试编译关键类...
javac -version
echo.

echo ========================================
echo 编译检查完成
echo ========================================
echo.
echo 如果没有错误信息，说明性能优化模块已准备就绪！
echo.
echo 下一步：
echo 1. 执行数据库索引脚本
echo 2. 在应用配置中启用 performance 配置
echo 3. 重启应用
echo.
pause
